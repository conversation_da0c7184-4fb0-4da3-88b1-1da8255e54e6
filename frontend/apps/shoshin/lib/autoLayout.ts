"use client";

import {
  getSmoothStepPath,
  Position,
  type Edge,
  type Node,
} from "@xyflow/react";

export interface LayoutOptions {
  horizontalSpacing?: number;
  verticalSpacing?: number;
  startX?: number;
  startY?: number;
  alignByLayer?: boolean;
  handleOrientation?: "horizontal" | "vertical" | "auto";
  animationDuration?: number;
  minimizeEdgeCrossings?: boolean;
  edgeCrossingIterations?: number;
  dynamicSpacing?: boolean;
  edgeNodeSpacing?: number;
  edgeNodeCollisionIterations?: number;
  onComplete?: (finalPositions: Map<string, { x: number; y: number }>) => void;
}

export interface AutoLayoutResult {
  positions: Map<string, { x: number; y: number }>;
  orientation: "horizontal" | "vertical";
}

/**
 * Detects the predominant handle orientation in the workflow
 */
export const detectHandleOrientation = (
  nodes: Node[],
): "horizontal" | "vertical" => {
  if (nodes.length === 0) return "horizontal";

  // For now, default to horizontal layout
  // This can be enhanced to detect based on node connections
  return "horizontal";
};

/**
 * Calculate the barycenter (average position) of connected nodes in adjacent levels
 */
const calculateBarycenter = (
  nodeId: string,
  level: number,
  levelNodes: Map<number, string[]>,
  adjacencyList: Map<string, string[]>,
  reverseAdjacencyList: Map<string, string[]>,
  orientation: "horizontal" | "vertical",
): number => {
  const currentLevelNodes = levelNodes.get(level) || [];
  const currentIndex = currentLevelNodes.indexOf(nodeId);

  // Get connected nodes from previous and next levels
  const connectedNodes: string[] = [];

  // Add nodes from previous level (sources)
  const prevLevelNodes = levelNodes.get(level - 1) || [];
  const sources = reverseAdjacencyList.get(nodeId) || [];
  sources.forEach((sourceId) => {
    if (prevLevelNodes.includes(sourceId)) {
      connectedNodes.push(sourceId);
    }
  });

  // Add nodes from next level (targets)
  const nextLevelNodes = levelNodes.get(level + 1) || [];
  const targets = adjacencyList.get(nodeId) || [];
  targets.forEach((targetId) => {
    if (nextLevelNodes.includes(targetId)) {
      connectedNodes.push(targetId);
    }
  });

  if (connectedNodes.length === 0) {
    return currentIndex; // Return current position if no connections
  }

  // Calculate average position of connected nodes
  let totalPosition = 0;
  connectedNodes.forEach((connectedId) => {
    // Find the position of connected node in its level
    for (const [connectedLevel, nodes] of levelNodes.entries()) {
      const connectedIndex = nodes.indexOf(connectedId);
      if (connectedIndex !== -1) {
        totalPosition += connectedIndex;
        break;
      }
    }
  });

  return totalPosition / connectedNodes.length;
};

/**
 * Minimize edge crossings using barycenter heuristic
 */
const minimizeEdgeCrossingsWithBarycenter = (
  levelNodes: Map<number, string[]>,
  adjacencyList: Map<string, string[]>,
  reverseAdjacencyList: Map<string, string[]>,
  orientation: "horizontal" | "vertical",
  iterations: number = 3,
): Map<number, string[]> => {
  const optimizedLevelNodes = new Map<number, string[]>();

  // Copy original level nodes
  levelNodes.forEach((nodes, level) => {
    optimizedLevelNodes.set(level, [...nodes]);
  });

  // Apply barycenter heuristic for specified iterations
  for (let iter = 0; iter < iterations; iter++) {
    // Process levels in alternating directions
    const levels = Array.from(optimizedLevelNodes.keys()).sort((a, b) => a - b);

    for (const level of levels) {
      const nodes = optimizedLevelNodes.get(level) || [];
      if (nodes.length <= 1) continue;

      // Calculate barycenter for each node
      const nodeBarycenter = nodes.map((nodeId) => ({
        nodeId,
        barycenter: calculateBarycenter(
          nodeId,
          level,
          optimizedLevelNodes,
          adjacencyList,
          reverseAdjacencyList,
          orientation,
        ),
      }));

      // Sort nodes by barycenter
      nodeBarycenter.sort((a, b) => a.barycenter - b.barycenter);

      // Update level with sorted nodes
      optimizedLevelNodes.set(
        level,
        nodeBarycenter.map((item) => item.nodeId),
      );
    }
  }

  return optimizedLevelNodes;
};

/**
 * Calculate dynamic spacing based on edge density between levels
 */
const calculateDynamicSpacing = (
  levelNodes: Map<number, string[]>,
  adjacencyList: Map<string, string[]>,
  baseSpacing: number,
  orientation: "horizontal" | "vertical",
): number => {
  let maxEdgeCount = 0;

  // Count edges between adjacent levels
  for (const [level, nodes] of levelNodes.entries()) {
    const nextLevelNodes = levelNodes.get(level + 1);
    if (!nextLevelNodes) continue;

    let edgeCount = 0;
    nodes.forEach((nodeId) => {
      const targets = adjacencyList.get(nodeId) || [];
      targets.forEach((targetId) => {
        if (nextLevelNodes.includes(targetId)) {
          edgeCount++;
        }
      });
    });

    maxEdgeCount = Math.max(maxEdgeCount, edgeCount);
  }

  // Increase spacing based on edge density
  const spacingMultiplier = Math.max(1, Math.sqrt(maxEdgeCount / 3));
  return Math.round(baseSpacing * spacingMultiplier);
};

/**
 * Node dimensions and bounding box utilities
 */
interface NodeBounds {
  x: number;
  y: number;
  width: number;
  height: number;
}

// Default node dimensions based on CustomNode component
const DEFAULT_NODE_WIDTH = 240; // min-w-[240px] from CustomNode
const DEFAULT_NODE_HEIGHT = 80; // Estimated height with padding

/**
 * Get bounding box for a node with padding
 */
const getNodeBounds = (
  nodeId: string,
  positions: Map<string, { x: number; y: number }>,
  padding: number = 0,
): NodeBounds => {
  const position = positions.get(nodeId);
  if (!position) {
    return {
      x: 0,
      y: 0,
      width: DEFAULT_NODE_WIDTH,
      height: DEFAULT_NODE_HEIGHT,
    };
  }

  return {
    x: position.x - padding,
    y: position.y - padding,
    width: DEFAULT_NODE_WIDTH + padding * 2,
    height: DEFAULT_NODE_HEIGHT + padding * 2,
  };
};

/**
 * Calculate edge path segments for collision detection
 */
const getEdgePathSegments = (
  sourceX: number,
  sourceY: number,
  targetX: number,
  targetY: number,
  orientation: "horizontal" | "vertical",
): Array<{ x1: number; y1: number; x2: number; y2: number }> => {
  const isHorizontal = orientation === "horizontal";
  const offset = isHorizontal ? 50 : 40; // Increased offset for better path representation

  // Create more accurate segments that represent the smooth step path
  const segments: Array<{ x1: number; y1: number; x2: number; y2: number }> =
    [];

  if (isHorizontal) {
    // Horizontal layout: source -> right -> down/up -> right -> target
    const midX1 = sourceX + offset;
    const midX2 = targetX - offset;

    // Ensure we have proper intermediate points
    const actualMidX1 = Math.min(midX1, midX2 - 20);
    const actualMidX2 = Math.max(midX2, midX1 + 20);

    segments.push(
      { x1: sourceX, y1: sourceY, x2: actualMidX1, y2: sourceY }, // Horizontal from source
      { x1: actualMidX1, y1: sourceY, x2: actualMidX1, y2: targetY }, // Vertical connector
      { x1: actualMidX1, y1: targetY, x2: actualMidX2, y2: targetY }, // Horizontal middle
      { x1: actualMidX2, y1: targetY, x2: actualMidX2, y2: targetY }, // Vertical to target level (if needed)
      { x1: actualMidX2, y1: targetY, x2: targetX, y2: targetY }, // Horizontal to target
    );
  } else {
    // Vertical layout: source -> down -> right/left -> up -> target
    const midY1 = sourceY + offset;
    const midY2 = targetY - offset;

    // Ensure we have proper intermediate points
    const actualMidY1 = Math.min(midY1, midY2 - 20);
    const actualMidY2 = Math.max(midY2, midY1 + 20);

    segments.push(
      { x1: sourceX, y1: sourceY, x2: sourceX, y2: actualMidY1 }, // Vertical from source
      { x1: sourceX, y1: actualMidY1, x2: targetX, y2: actualMidY1 }, // Horizontal connector
      { x1: targetX, y1: actualMidY1, x2: targetX, y2: actualMidY2 }, // Vertical middle
      { x1: targetX, y1: actualMidY2, x2: targetX, y2: targetY }, // Vertical to target
    );
  }

  // Filter out zero-length segments
  return segments.filter(
    (segment) =>
      Math.abs(segment.x2 - segment.x1) > 1 ||
      Math.abs(segment.y2 - segment.y1) > 1,
  );
};

/**
 * Check if a line segment intersects with a rectangle
 */
const lineIntersectsRect = (
  line: { x1: number; y1: number; x2: number; y2: number },
  rect: NodeBounds,
): boolean => {
  const { x1, y1, x2, y2 } = line;
  const { x, y, width, height } = rect;

  // Check if line endpoints are inside rectangle
  const p1Inside = x1 >= x && x1 <= x + width && y1 >= y && y1 <= y + height;
  const p2Inside = x2 >= x && x2 <= x + width && y2 >= y && y2 <= y + height;

  if (p1Inside || p2Inside) return true;

  // Check line intersection with rectangle edges
  const rectLines = [
    { x1: x, y1: y, x2: x + width, y2: y }, // Top edge
    { x1: x + width, y1: y, x2: x + width, y2: y + height }, // Right edge
    { x1: x + width, y1: y + height, x2: x, y2: y + height }, // Bottom edge
    { x1: x, y1: y + height, x2: x, y2: y }, // Left edge
  ];

  return rectLines.some((rectLine) => linesIntersect(line, rectLine));
};

/**
 * Check if two line segments intersect
 */
const linesIntersect = (
  line1: { x1: number; y1: number; x2: number; y2: number },
  line2: { x1: number; y1: number; x2: number; y2: number },
): boolean => {
  const { x1: x1a, y1: y1a, x2: x2a, y2: y2a } = line1;
  const { x1: x1b, y1: y1b, x2: x2b, y2: y2b } = line2;

  const denom = (y2b - y1b) * (x2a - x1a) - (x2b - x1b) * (y2a - y1a);
  if (denom === 0) return false; // Lines are parallel

  const ua = ((x2b - x1b) * (y1a - y1b) - (y2b - y1b) * (x1a - x1b)) / denom;
  const ub = ((x2a - x1a) * (y1a - y1b) - (y2a - y1a) * (x1a - x1b)) / denom;

  return ua >= 0 && ua <= 1 && ub >= 0 && ub <= 1;
};

/**
 * Detect collisions between edges and nodes using a more robust approach
 */
const detectEdgeNodeCollisions = (
  nodes: Node[],
  edges: Edge[],
  positions: Map<string, { x: number; y: number }>,
  orientation: "horizontal" | "vertical",
  minSpacing: number,
): Array<{
  nodeId: string;
  edgeId: string;
  adjustmentVector: { x: number; y: number };
}> => {
  const collisions: Array<{
    nodeId: string;
    edgeId: string;
    adjustmentVector: { x: number; y: number };
  }> = [];

  console.log(
    `🔍 Checking ${edges.length} edges against ${nodes.length} nodes for collisions`,
  );

  // Check each edge against each node (except source and target)
  edges.forEach((edge) => {
    const sourcePos = positions.get(edge.source);
    const targetPos = positions.get(edge.target);

    if (!sourcePos || !targetPos) {
      console.log(
        `⚠️ Missing position for edge ${edge.id}: source=${!!sourcePos}, target=${!!targetPos}`,
      );
      return;
    }

    // Calculate edge center points for handle positions
    const sourceCenterX = sourcePos.x + DEFAULT_NODE_WIDTH / 2;
    const sourceCenterY = sourcePos.y + DEFAULT_NODE_HEIGHT / 2;
    const targetCenterX = targetPos.x + DEFAULT_NODE_WIDTH / 2;
    const targetCenterY = targetPos.y + DEFAULT_NODE_HEIGHT / 2;

    console.log(
      `📏 Edge ${edge.id}: (${sourceCenterX.toFixed(1)}, ${sourceCenterY.toFixed(1)}) -> (${targetCenterX.toFixed(1)}, ${targetCenterY.toFixed(1)})`,
    );

    // Check collision with each node (except source and target)
    nodes.forEach((node) => {
      if (node.id === edge.source || node.id === edge.target) return;

      const nodePos = positions.get(node.id);
      if (!nodePos) return;

      // Get node bounds with padding
      const nodeBounds = {
        x: nodePos.x - minSpacing,
        y: nodePos.y - minSpacing,
        width: DEFAULT_NODE_WIDTH + minSpacing * 2,
        height: DEFAULT_NODE_HEIGHT + minSpacing * 2,
      };

      console.log(
        `🔍 Checking node ${node.id} bounds: (${nodeBounds.x.toFixed(1)}, ${nodeBounds.y.toFixed(1)}) ${nodeBounds.width}x${nodeBounds.height}`,
      );

      // Simplified collision detection: check if edge path intersects with expanded node bounds
      let hasCollision = false;
      let adjustmentVector = { x: 0, y: 0 };

      if (orientation === "horizontal") {
        // For horizontal layout, check if the edge path goes through the node area
        const edgeMinX = Math.min(sourceCenterX, targetCenterX);
        const edgeMaxX = Math.max(sourceCenterX, targetCenterX);
        const edgeMinY = Math.min(sourceCenterY, targetCenterY);
        const edgeMaxY = Math.max(sourceCenterY, targetCenterY);

        // Check if node overlaps with the edge's bounding box
        const nodeOverlapsX =
          nodeBounds.x < edgeMaxX && nodeBounds.x + nodeBounds.width > edgeMinX;
        const nodeOverlapsY =
          nodeBounds.y < edgeMaxY &&
          nodeBounds.y + nodeBounds.height > edgeMinY;

        if (nodeOverlapsX && nodeOverlapsY) {
          hasCollision = true;

          // Calculate adjustment to move node away from edge path
          const nodeCenter = {
            x: nodePos.x + DEFAULT_NODE_WIDTH / 2,
            y: nodePos.y + DEFAULT_NODE_HEIGHT / 2,
          };

          // Determine best direction to move the node
          const edgeCenterX = (sourceCenterX + targetCenterX) / 2;
          const edgeCenterY = (sourceCenterY + targetCenterY) / 2;

          const deltaX = nodeCenter.x - edgeCenterX;
          const deltaY = nodeCenter.y - edgeCenterY;

          // Move in the direction that creates the most separation
          const moveDistance = minSpacing + 50; // Extra buffer

          if (Math.abs(deltaX) > Math.abs(deltaY)) {
            // Move horizontally
            adjustmentVector.x = deltaX > 0 ? moveDistance : -moveDistance;
            adjustmentVector.y = deltaY * 0.3; // Small vertical adjustment
          } else {
            // Move vertically
            adjustmentVector.y = deltaY > 0 ? moveDistance : -moveDistance;
            adjustmentVector.x = deltaX * 0.3; // Small horizontal adjustment
          }
        }
      } else {
        // Similar logic for vertical layout
        const edgeMinX = Math.min(sourceCenterX, targetCenterX);
        const edgeMaxX = Math.max(sourceCenterX, targetCenterX);
        const edgeMinY = Math.min(sourceCenterY, targetCenterY);
        const edgeMaxY = Math.max(sourceCenterY, targetCenterY);

        const nodeOverlapsX =
          nodeBounds.x < edgeMaxX && nodeBounds.x + nodeBounds.width > edgeMinX;
        const nodeOverlapsY =
          nodeBounds.y < edgeMaxY &&
          nodeBounds.y + nodeBounds.height > edgeMinY;

        if (nodeOverlapsX && nodeOverlapsY) {
          hasCollision = true;

          const nodeCenter = {
            x: nodePos.x + DEFAULT_NODE_WIDTH / 2,
            y: nodePos.y + DEFAULT_NODE_HEIGHT / 2,
          };

          const edgeCenterX = (sourceCenterX + targetCenterX) / 2;
          const edgeCenterY = (sourceCenterY + targetCenterY) / 2;

          const deltaX = nodeCenter.x - edgeCenterX;
          const deltaY = nodeCenter.y - edgeCenterY;

          const moveDistance = minSpacing + 50;

          if (Math.abs(deltaY) > Math.abs(deltaX)) {
            adjustmentVector.y = deltaY > 0 ? moveDistance : -moveDistance;
            adjustmentVector.x = deltaX * 0.3;
          } else {
            adjustmentVector.x = deltaX > 0 ? moveDistance : -moveDistance;
            adjustmentVector.y = deltaY * 0.3;
          }
        }
      }

      if (hasCollision) {
        console.log(
          `💥 Collision detected: Node ${node.id} overlaps with edge ${edge.id}`,
        );
        console.log(`🎯 Calculated adjustment for node ${node.id}:`, {
          adjustmentVector: {
            x: adjustmentVector.x.toFixed(2),
            y: adjustmentVector.y.toFixed(2),
          },
          requiredSpacing: minSpacing + 50,
        });

        collisions.push({
          nodeId: node.id,
          edgeId: edge.id,
          adjustmentVector,
        });
      }
    });
  });

  console.log(`📊 Total collisions found: ${collisions.length}`);
  return collisions;
};

/**
 * Get closest point on a line segment to a given point
 */
const getClosestPointOnLine = (
  point: { x: number; y: number },
  line: { x1: number; y1: number; x2: number; y2: number },
): { x: number; y: number } => {
  const { x, y } = point;
  const { x1, y1, x2, y2 } = line;

  const dx = x2 - x1;
  const dy = y2 - y1;
  const length = dx * dx + dy * dy;

  if (length === 0) return { x: x1, y: y1 };

  const t = Math.max(0, Math.min(1, ((x - x1) * dx + (y - y1) * dy) / length));

  return {
    x: x1 + t * dx,
    y: y1 + t * dy,
  };
};

/**
 * Calculates auto-layout positions for nodes with improved spacing and alignment
 */
export const calculateAutoLayout = (
  nodes: Node[],
  edges: Edge[],
  options: LayoutOptions = {},
): AutoLayoutResult => {
  const {
    horizontalSpacing = 300,
    verticalSpacing = 200,
    startX = 100,
    startY = 100,
    alignByLayer = true,
    handleOrientation = "auto",
    minimizeEdgeCrossings = true,
    edgeCrossingIterations = 3,
    dynamicSpacing = true,
    edgeNodeSpacing = 50,
    edgeNodeCollisionIterations = 3,
  } = options;

  if (nodes.length === 0) {
    return { positions: new Map(), orientation: "horizontal" };
  }

  const orientation =
    handleOrientation === "auto"
      ? detectHandleOrientation(nodes)
      : handleOrientation;

  // Create adjacency list and calculate in-degrees
  const adjacencyList = new Map<string, string[]>();
  const reverseAdjacencyList = new Map<string, string[]>();
  const inDegree = new Map<string, number>();

  // Initialize
  nodes.forEach((node) => {
    adjacencyList.set(node.id, []);
    reverseAdjacencyList.set(node.id, []);
    inDegree.set(node.id, 0);
  });

  // Build graph
  edges.forEach((edge) => {
    if (edge.source && edge.target) {
      adjacencyList.get(edge.source)?.push(edge.target);
      reverseAdjacencyList.get(edge.target)?.push(edge.source);
      inDegree.set(edge.target, (inDegree.get(edge.target) || 0) + 1);
    }
  });

  // Topological sort to determine levels
  const levels = new Map<string, number>();
  const levelNodes = new Map<number, string[]>();
  const queue: string[] = [];

  // Find root nodes (nodes with no incoming edges)
  nodes.forEach((node) => {
    if (inDegree.get(node.id) === 0) {
      queue.push(node.id);
      levels.set(node.id, 0);
    }
  });

  // If no root nodes, use the first node as root
  if (queue.length === 0 && nodes.length > 0) {
    const firstNode = nodes[0];
    queue.push(firstNode.id);
    levels.set(firstNode.id, 0);
  }

  // BFS to assign levels
  while (queue.length > 0) {
    const nodeId = queue.shift()!;
    const currentLevel = levels.get(nodeId) || 0;

    // Add to level tracking
    if (!levelNodes.has(currentLevel)) {
      levelNodes.set(currentLevel, []);
    }
    levelNodes.get(currentLevel)!.push(nodeId);

    // Process children
    const children = adjacencyList.get(nodeId) || [];
    children.forEach((childId) => {
      const childLevel = Math.max(levels.get(childId) || 0, currentLevel + 1);
      levels.set(childId, childLevel);

      // Decrease in-degree and add to queue if ready
      const newInDegree = (inDegree.get(childId) || 0) - 1;
      inDegree.set(childId, newInDegree);

      if (newInDegree === 0) {
        queue.push(childId);
      }
    });
  }

  // Handle any remaining nodes (cycles or disconnected components)
  nodes.forEach((node) => {
    if (!levels.has(node.id)) {
      const maxLevel = Math.max(...Array.from(levels.values()), -1);
      levels.set(node.id, maxLevel + 1);

      const level = maxLevel + 1;
      if (!levelNodes.has(level)) {
        levelNodes.set(level, []);
      }
      levelNodes.get(level)!.push(node.id);
    }
  });

  // Apply edge crossing minimization if enabled
  let optimizedLevelNodes = levelNodes;
  if (minimizeEdgeCrossings && edges.length > 0) {
    optimizedLevelNodes = minimizeEdgeCrossingsWithBarycenter(
      levelNodes,
      adjacencyList,
      reverseAdjacencyList,
      orientation,
      edgeCrossingIterations,
    );
  }

  // Calculate dynamic spacing if enabled
  const finalHorizontalSpacing = dynamicSpacing
    ? calculateDynamicSpacing(
        optimizedLevelNodes,
        adjacencyList,
        horizontalSpacing,
        orientation,
      )
    : horizontalSpacing;
  const finalVerticalSpacing = dynamicSpacing
    ? calculateDynamicSpacing(
        optimizedLevelNodes,
        adjacencyList,
        verticalSpacing,
        orientation,
      )
    : verticalSpacing;

  // Calculate positions based on orientation
  const positions = new Map<string, { x: number; y: number }>();

  if (orientation === "horizontal") {
    // Horizontal layout (left to right)
    optimizedLevelNodes.forEach((nodeIds, level) => {
      const totalNodesInLevel = nodeIds.length;
      const levelHeight = (totalNodesInLevel - 1) * finalVerticalSpacing;
      const startYForLevel = startY - levelHeight / 2;

      nodeIds.forEach((nodeId, indexInLevel) => {
        positions.set(nodeId, {
          x: startX + level * finalHorizontalSpacing,
          y: startYForLevel + indexInLevel * finalVerticalSpacing,
        });
      });
    });
  } else {
    // Vertical layout (top to bottom)
    optimizedLevelNodes.forEach((nodeIds, level) => {
      const totalNodesInLevel = nodeIds.length;
      const levelWidth = (totalNodesInLevel - 1) * finalHorizontalSpacing;
      const startXForLevel = startX - levelWidth / 2;

      nodeIds.forEach((nodeId, indexInLevel) => {
        positions.set(nodeId, {
          x: startXForLevel + indexInLevel * finalHorizontalSpacing,
          y: startY + level * finalVerticalSpacing,
        });
      });
    });
  }

  // Apply edge-node collision detection and adjustment
  if (edgeNodeSpacing > 0 && edges.length > 0) {
    console.log("🔍 Starting edge-node collision detection", {
      edgeNodeSpacing,
      edgeNodeCollisionIterations,
      nodesCount: nodes.length,
      edgesCount: edges.length,
    });

    let adjustedPositions = new Map(positions);
    let totalCollisionsResolved = 0;

    for (
      let iteration = 0;
      iteration < edgeNodeCollisionIterations;
      iteration++
    ) {
      const collisions = detectEdgeNodeCollisions(
        nodes,
        edges,
        adjustedPositions,
        orientation,
        edgeNodeSpacing,
      );

      console.log(
        `📊 Iteration ${iteration + 1}: Found ${collisions.length} edge-node collisions`,
      );

      if (collisions.length === 0) {
        console.log("✅ No more collisions detected, stopping early");
        break; // No more collisions
      }

      totalCollisionsResolved += collisions.length;

      // Apply adjustments
      const adjustments = new Map<string, { x: number; y: number }>();

      collisions.forEach(({ nodeId, edgeId, adjustmentVector }) => {
        const currentAdjustment = adjustments.get(nodeId) || { x: 0, y: 0 };
        adjustments.set(nodeId, {
          x: currentAdjustment.x + adjustmentVector.x,
          y: currentAdjustment.y + adjustmentVector.y,
        });

        console.log(`🔧 Adjusting node ${nodeId} away from edge ${edgeId}:`, {
          adjustmentVector,
          totalAdjustment: {
            x: currentAdjustment.x + adjustmentVector.x,
            y: currentAdjustment.y + adjustmentVector.y,
          },
        });
      });

      // Apply accumulated adjustments
      adjustments.forEach((adjustment, nodeId) => {
        const currentPos = adjustedPositions.get(nodeId);
        if (currentPos) {
          const newPos = {
            x: currentPos.x + adjustment.x,
            y: currentPos.y + adjustment.y,
          };
          adjustedPositions.set(nodeId, newPos);

          console.log(
            `📍 Node ${nodeId} moved from (${currentPos.x.toFixed(1)}, ${currentPos.y.toFixed(1)}) to (${newPos.x.toFixed(1)}, ${newPos.y.toFixed(1)})`,
          );
        }
      });
    }

    console.log("🎯 Edge-node collision resolution completed", {
      totalCollisionsResolved,
      finalIterations: edgeNodeCollisionIterations,
    });

    return { positions: adjustedPositions, orientation };
  }

  return { positions, orientation };
};

/**
 * Easing function for smooth animations
 */
export const easeOutCubic = (t: number): number => 1 - (1 - t) ** 3;

/**
 * Enhanced auto-layout function with smooth animations
 */
export const applyAutoLayoutSmooth = (
  nodes: Node[],
  edges: Edge[],
  updateNodes: (nodes: Node[]) => void,
  fitView?: (options?: { padding?: number; duration?: number }) => void,
  options: LayoutOptions = {},
): void => {
  const { animationDuration = 500, onComplete, ...layoutOptions } = options;

  if (nodes.length === 0) return;

  const { positions: targetPositions, orientation } = calculateAutoLayout(
    nodes,
    edges,
    layoutOptions,
  );

  if (targetPositions.size === 0) return;

  // Store initial positions
  const initialPositions = new Map<string, { x: number; y: number }>();
  nodes.forEach((node) => {
    initialPositions.set(node.id, { x: node.position.x, y: node.position.y });
  });

  const startTime = Date.now();

  const animate = () => {
    const elapsed = Date.now() - startTime;
    const progress = Math.min(elapsed / animationDuration, 1);
    const easedProgress = easeOutCubic(progress);

    // Update node positions
    const updatedNodes = nodes.map((node) => {
      const initialPos = initialPositions.get(node.id);
      const targetPos = targetPositions.get(node.id);

      if (!initialPos || !targetPos) return node;

      const newPosition = {
        x: initialPos.x + (targetPos.x - initialPos.x) * easedProgress,
        y: initialPos.y + (targetPos.y - initialPos.y) * easedProgress,
      };

      return {
        ...node,
        position: newPosition,
      };
    });

    updateNodes(updatedNodes);

    if (progress < 1) {
      requestAnimationFrame(animate);
    } else {
      // Animation complete
      if (fitView) {
        fitView({
          padding: 0.2,
          duration: 400,
        });
      }

      if (onComplete) {
        onComplete(targetPositions);
      }
    }
  };

  animate();
};

/**
 * Debounced auto layout to prevent rapid triggering
 */
export const createDebouncedAutoLayout = (
  autoLayoutFn: () => void,
  delay: number = 250,
) => {
  let timeoutId: NodeJS.Timeout | null = null;

  return () => {
    if (timeoutId) {
      clearTimeout(timeoutId);
    }

    timeoutId = setTimeout(() => {
      autoLayoutFn();
      timeoutId = null;
    }, delay);

    // Return cleanup function
    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
        timeoutId = null;
      }
    };
  };
};
